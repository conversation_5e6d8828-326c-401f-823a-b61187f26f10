import java.util.List;
import java.util.Map;

/**
 * Holds the results of a scheduling simulation
 */
public class SchedulingResult {
    private List<Process> processes;
    private List<String> history;
    private Map<String, Integer> waitTimes;
    private Map<String, Integer> turnaroundTimes;
    
    public SchedulingResult(List<Process> processes, List<String> history, 
                           Map<String, Integer> waitTimes, Map<String, Integer> turnaroundTimes) {
        this.processes = processes;
        this.history = history;
        this.waitTimes = waitTimes;
        this.turnaroundTimes = turnaroundTimes;
    }
    
    // Getters
    public List<Process> getProcesses() { return processes; }
    public List<String> getHistory() { return history; }
    public Map<String, Integer> getWaitTimes() { return waitTimes; }
    public Map<String, Integer> getTurnaroundTimes() { return turnaroundTimes; }
    
    /**
     * Prints the results in the same format as the original Python code
     */
    public void printResults() {
        System.out.println();
        for (String historyEntry : history) {
            System.out.println(historyEntry);
        }
        System.out.println();
        System.out.println("Process Turnarounds Waits ");
        for (Process process : processes) {
            String name = process.getName();
            System.out.printf("%-8s%-12d%d%n", 
                            name, 
                            turnaroundTimes.get(name), 
                            waitTimes.get(name));
        }
    }
}

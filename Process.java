/**
 * Represents a process in the scheduling simulation
 */
public class Process {
    private String name;
    private int id;
    private int arrivalTime;
    private int serviceTime;
    private int remainingTime;
    
    public Process(String name, int id, int arrivalTime, int serviceTime) {
        this.name = name;
        this.id = id;
        this.arrivalTime = arrivalTime;
        this.serviceTime = serviceTime;
        this.remainingTime = serviceTime;
    }
    
    // Copy constructor for creating process copies
    public Process(Process other) {
        this.name = other.name;
        this.id = other.id;
        this.arrivalTime = other.arrivalTime;
        this.serviceTime = other.serviceTime;
        this.remainingTime = other.serviceTime; // Reset remaining time
    }
    
    // Getters
    public String getName() { return name; }
    public int getId() { return id; }
    public int getArrivalTime() { return arrivalTime; }
    public int getServiceTime() { return serviceTime; }
    public int getRemainingTime() { return remainingTime; }
    
    // Setters
    public void setRemainingTime(int remainingTime) { this.remainingTime = remainingTime; }
    
    // Utility methods
    public void decrementRemainingTime() { this.remainingTime--; }
    public boolean isCompleted() { return remainingTime <= 0; }
    
    @Override
    public String toString() {
        return String.format("Process{name='%s', id=%d, arrival=%d, service=%d, remaining=%d}", 
                           name, id, arrivalTime, serviceTime, remainingTime);
    }
}

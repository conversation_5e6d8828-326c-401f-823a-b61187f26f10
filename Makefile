# Makefile for CPU Scheduling Simulation Java Project

# Java compiler
JAVAC = javac
JAVA = java

# Source files
SOURCES = Process.java SchedulingResult.java DataFileReader.java SchedulingAlgorithms.java SelfishRoundRobin.java SchedulingSimulation.java

# Main class
MAIN_CLASS = SchedulingSimulation

# Default target
all: compile

# Compile all Java files
compile:
	$(JAVAC) $(SOURCES)

# Run the simulation
run: compile
	$(JAVA) $(MAIN_CLASS)

# Clean compiled files
clean:
	rm -f *.class

# Help target
help:
	@echo "Available targets:"
	@echo "  compile - Compile all Java source files"
	@echo "  run     - Compile and run the scheduling simulation"
	@echo "  clean   - Remove all compiled .class files"
	@echo "  help    - Show this help message"

.PHONY: all compile run clean help

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Reads and parses the data file containing process information
 */
public class DataFileReader {
    
    /**
     * Reads the data file and returns a ProcessData object containing processes and dispatch time
     */
    public static ProcessData readDataFile(String filename) throws IOException {
        List<Process> processes = new ArrayList<>();
        int dispatchTime = 0;
        
        try (BufferedReader reader = new BufferedReader(new FileReader(filename))) {
            List<String[]> data = new ArrayList<>();
            String line;
            
            // Read all lines and split them
            while ((line = reader.readLine()) != null) {
                String[] parts = line.trim().split("\\s+");
                if (parts.length > 0 && !parts[0].isEmpty()) {
                    data.add(parts);
                }
            }
            
            // Find dispatch time
            for (String[] parts : data) {
                if (parts.length >= 2 && "DISP:".equals(parts[0])) {
                    try {
                        int value = Integer.parseInt(parts[1]);
                        if (value >= 0) {
                            dispatchTime = value;
                            break;
                        }
                    } catch (NumberFormatException e) {
                        System.err.println("Error: Could not identify a valid dispatcher time");
                        break;
                    }
                }
            }
            
            // Parse processes
            for (int i = 0; i < data.size(); i++) {
                String[] currentLine = data.get(i);
                if (currentLine.length >= 2 && "PID:".equals(currentLine[0])) {
                    try {
                        String name = currentLine[1];
                        int id = Integer.parseInt(name.substring(1)); // Remove 'p' prefix
                        
                        // Get arrival time from next line
                        int arrivalTime = 0;
                        if (i + 1 < data.size() && data.get(i + 1).length >= 2 && 
                            "ArrTime:".equals(data.get(i + 1)[0])) {
                            arrivalTime = Integer.parseInt(data.get(i + 1)[1]);
                        }
                        
                        // Get service time from line after that
                        int serviceTime = 0;
                        if (i + 2 < data.size() && data.get(i + 2).length >= 2 && 
                            "SrvTime:".equals(data.get(i + 2)[0])) {
                            serviceTime = Integer.parseInt(data.get(i + 2)[1]);
                        }
                        
                        processes.add(new Process(name, id, arrivalTime, serviceTime));
                    } catch (NumberFormatException | StringIndexOutOfBoundsException e) {
                        System.err.println("Error parsing process data at line " + i);
                    }
                }
            }
        }
        
        return new ProcessData(processes, dispatchTime);
    }
    
    /**
     * Inner class to hold the parsed data
     */
    public static class ProcessData {
        private final List<Process> processes;
        private final int dispatchTime;
        
        public ProcessData(List<Process> processes, int dispatchTime) {
            this.processes = processes;
            this.dispatchTime = dispatchTime;
        }
        
        public List<Process> getProcesses() { return processes; }
        public int getDispatchTime() { return dispatchTime; }
    }
}

# CPU Scheduling Simulation - Java Version

This project is a Java conversion of the original Python `scheduling.py` program. It simulates three different CPU scheduling algorithms:

1. **FCFS** (First Come First Served)
2. **RR** (Round Robin with quantum = 3)
3. **SRR** (Selfish Round Robin with dynamic quantum)

## Project Structure

- `Process.java` - Represents a process with arrival time, service time, and other attributes
- `SchedulingResult.java` - Holds the results of a scheduling simulation
- `DataFileReader.java` - Reads and parses the input data file
- `SchedulingAlgorithms.java` - Contains FCFS and Round Robin implementations
- `SelfishRoundRobin.java` - Contains the Selfish Round Robin implementation
- `SchedulingSimulation.java` - Main class that runs all three algorithms
- `datafile1.txt` - Input data file with process information
- `Makefile` - Build automation

## Input File Format

The input file (`datafile1.txt`) should follow this format:

```
BEGIN

DISP: 1
END

PID: p1
ArrTime: 0
SrvTime: 10
END

PID: p2
ArrTime: 0
SrvTime: 1
END

...

EOF
```

## How to Build and Run

### Using Makefile (Recommended)

```bash
# Compile all Java files
make compile

# Compile and run the simulation
make run

# Clean compiled files
make clean

# Show help
make help
```

### Manual Compilation

```bash
# Compile all Java files
javac *.java

# Run the simulation
java SchedulingSimulation
```

## Output

The program outputs the scheduling results for each algorithm, showing:
- The execution timeline (when each process starts)
- Process turnaround times
- Process wait times

## Key Differences from Python Version

1. **Object-Oriented Design**: Uses proper Java classes for Process, SchedulingResult, etc.
2. **Type Safety**: Strong typing with proper data structures
3. **Error Handling**: Proper exception handling for file I/O and parsing
4. **Immutability**: Original process list is preserved by creating copies for each algorithm
5. **Separation of Concerns**: Each algorithm and utility is in its own class

## Algorithm Details

- **FCFS**: Processes are executed in order of arrival time, then by process ID
- **RR**: Each process gets a time quantum of 3 units before being preempted
- **SRR**: Like RR, but quantum increases by 1 each time a process is preempted (max 6)

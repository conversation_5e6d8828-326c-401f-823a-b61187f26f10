@echo off
REM Windows batch file for CPU Scheduling Simulation Java Project

if "%1"=="compile" goto compile
if "%1"=="run" goto run
if "%1"=="clean" goto clean
if "%1"=="help" goto help
if "%1"=="" goto run

:help
echo Available commands:
echo   build.bat compile - Compile all Java source files
echo   build.bat run [filename] - Compile and run the scheduling simulation
echo   build.bat clean   - Remove all compiled .class files
echo   build.bat help    - Show this help message
echo.
echo Examples:
echo   build.bat run                - Run with default datafile1.txt
echo   build.bat run datafile2.txt  - Run with specific file
goto end

:compile
echo Compiling Java files...
javac *.java
if %errorlevel% equ 0 (
    echo Compilation successful!
) else (
    echo Compilation failed!
)
goto end

:run
echo Compiling and running Java project...
javac *.java
if %errorlevel% equ 0 (
    echo Running A1...
    if "%2"=="" (
        java A1
    ) else (
        java A1 %2
    )
) else (
    echo Compilation failed!
)
goto end

:clean
echo Cleaning compiled files...
del *.class 2>nul
echo Clean complete!
goto end

:end

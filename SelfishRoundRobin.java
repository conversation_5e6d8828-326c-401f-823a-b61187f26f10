import java.util.*;

/**
 * Selfish Round Robin (SRR) scheduling algorithm implementation
 */
public class SelfishRoundRobin {
    
    /**
     * Selfish Round Robin scheduling algorithm with dynamic quantum
     */
    public static SchedulingResult selfishRoundRobin(List<Process> originalProcesses, int dispatchTime) {
        // Create copies of processes to avoid modifying originals
        List<Process> processes = new ArrayList<>();
        for (Process p : originalProcesses) {
            processes.add(new Process(p));
        }
        
        // Sort processes by arrival time and id
        processes.sort((a, b) -> {
            int arrivalCompare = Integer.compare(a.getArrivalTime(), b.getArrivalTime());
            return arrivalCompare != 0 ? arrivalCompare : Integer.compare(a.getId(), b.getId());
        });
        
        Map<String, Integer> quantum = new HashMap<>();
        int currentQuantum = 0;
        int time = 0;
        Queue<Process> readyQueue = new LinkedList<>();
        String serverState = "DISPATCHING";
        Process currentProcess = null;
        List<Process> returnProcess = new ArrayList<>();
        Map<String, Integer> waitingTime = new HashMap<>();
        Map<String, Integer> turnaroundTime = new HashMap<>();
        List<String> processHistory = new ArrayList<>();
        processHistory.add("SRR:");
        
        // Add processes that arrive at time 0
        for (Process process : processes) {
            if (process.getArrivalTime() == 0) {
                readyQueue.offer(process);
                waitingTime.put(process.getName(), 0);
                quantum.put(process.getName(), 3);
            }
        }
        
        while (true) {
            if ("DISPATCHING".equals(serverState)) {
                if (readyQueue.isEmpty()) {
                    break;
                }
                
                // Increment wait time for all processes in ready queue
                for (Process process : readyQueue) {
                    waitingTime.put(process.getName(), waitingTime.get(process.getName()) + 1);
                }
                
                currentProcess = readyQueue.poll();
                processHistory.add("T" + (time + 1) + ": " + currentProcess.getName());
                serverState = "WORKING";
                currentQuantum = 0;
            } else if ("WORKING".equals(serverState)) {
                // Decrement the service time of the current process
                currentProcess.decrementRemainingTime();
                currentQuantum++;
                
                // If the current process is done
                if (currentProcess.isCompleted()) {
                    serverState = "DISPATCHING";
                    turnaroundTime.put(currentProcess.getName(), time + 1 - currentProcess.getArrivalTime());
                } else if (currentQuantum >= quantum.get(currentProcess.getName())) {
                    serverState = "DISPATCHING";
                    // Increase quantum for this process (selfish behavior)
                    int newQuantum = quantum.get(currentProcess.getName()) + 1;
                    quantum.put(currentProcess.getName(), Math.min(newQuantum, 6)); // Max quantum is 6
                    returnProcess.add(currentProcess);
                    currentProcess = null;
                }
                
                // Increment wait time for processes in ready queue
                for (Process process : readyQueue) {
                    waitingTime.put(process.getName(), waitingTime.get(process.getName()) + 1);
                }
            }
            
            time++;
            
            // Add processes that arrive at current time
            for (Process process : processes) {
                if (process.getArrivalTime() == time) {
                    readyQueue.offer(process);
                    waitingTime.put(process.getName(), 0);
                    quantum.put(process.getName(), 3); // Initial quantum is 3
                }
            }
            
            // Add returned processes back to ready queue
            if (!returnProcess.isEmpty()) {
                readyQueue.offer(returnProcess.remove(0));
            }
        }
        
        return new SchedulingResult(originalProcesses, processHistory, waitingTime, turnaroundTime);
    }
}

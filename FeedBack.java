import java.util.*;

/**
 * Feedback (FB) scheduling algorithm implementation
 */
public class FeedBack {
    
    /**
     * Feedback scheduling algorithm with dynamic quantum
     */
    public static SchedulingResult FeedBack(List<Process> originalProcesses, int dispatchTime) {
        // Create copies of processes to avoid modifying originals
        List<Process> processes = new ArrayList<>();
        for (Process p : originalProcesses) {
            processes.add(new Process(p));
        }
        
        // Sort processes by arrival time and id
        processes.sort((a, b) -> {
            int arrivalCompare = Integer.compare(a.getArrivalTime(), b.getArrivalTime());
            return arrivalCompare != 0 ? arrivalCompare : Integer.compare(a.getId(), b.getId());
        });
        
        final int QUANTUM = 3; // Constant quantum for all levels
        int currentQuantum = 0;
        int time = 0;

        // Replace single queue with 4 priority queues
        List<Queue<Process>> priorityQueues = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            priorityQueues.add(new LinkedList<>());
        }
        Map<String, Integer> processLevel = new HashMap<>(); // Track each process's level

        String serverState = "DISPATCHING";
        Process currentProcess = null;
        Map<String, Integer> waitingTime = new HashMap<>();
        Map<String, Integer> turnaroundTime = new HashMap<>();
        List<String> processHistory = new ArrayList<>();
        processHistory.add("FB:");
        
        // Add processes that arrive at time 0
        for (Process process : processes) {
            if (process.getArrivalTime() == 0) {
                priorityQueues.get(0).offer(process);
                waitingTime.put(process.getName(), 0);
                processLevel.put(process.getName(), 0);
            }
        }
        
        while (true) {
            if ("DISPATCHING".equals(serverState)) {
                // Check if ALL priority queues are empty
                boolean allQueuesEmpty = true;
                for (Queue<Process> queue : priorityQueues) {
                    if (!queue.isEmpty()) {
                        allQueuesEmpty = false;
                        break;
                    }
                }
                if (allQueuesEmpty) {
                    break;
                }
                
                
                // Find highest priority queue with processes
                int currentLevel = -1;
                for (int i = 0; i < 4; i++) {
                    if (!priorityQueues.get(i).isEmpty()) {
                        currentLevel = i;
                        break;
                    }
                }
                currentProcess = priorityQueues.get(currentLevel).poll();

                processHistory.add("T" + (time + 1) + ": " + currentProcess.getName());
                serverState = "WORKING";
                currentQuantum = 0;
            } else if ("WORKING".equals(serverState)) {
                // Decrement the service time of the current process
                currentProcess.decrementRemainingTime();
                currentQuantum++;
                
                // If the current process is done
                if (currentProcess.isCompleted()) {
                    serverState = "DISPATCHING";
                    turnaroundTime.put(currentProcess.getName(), time + 1 - currentProcess.getArrivalTime());
                } else if (currentQuantum >= QUANTUM) {
                    serverState = "DISPATCHING";
                    // Demote to next lower level (or stay at level 3)
                    int currentLevel = processLevel.get(currentProcess.getName());
                    int newLevel = Math.min(currentLevel + 1, 3);
                    priorityQueues.get(newLevel).offer(currentProcess);
                    processLevel.put(currentProcess.getName(), newLevel);
                    currentProcess = null;
                }
                
                // Increment wait time for all processes in all priority queues
                for (Queue<Process> queue : priorityQueues) {
                    for (Process process : queue) {
                        waitingTime.put(process.getName(), waitingTime.get(process.getName()) + 1);
                    }
                }
            }
            
            time++;
            
            // Add processes that arrive at current time
            for (Process process : processes) {
                if (process.getArrivalTime() == time) {
                    priorityQueues.get(0).offer(process);
                    waitingTime.put(process.getName(), 0);
                    processLevel.put(process.getName(), 0);
                }
            }
            
        }
        
        return new SchedulingResult(originalProcesses, processHistory, waitingTime, turnaroundTime);
    }
}

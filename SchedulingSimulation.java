import java.io.IOException;
import java.util.List;

/**
 * Main class for the CPU scheduling simulation
 * Converts the original Python scheduling.py to Java
 */
public class SchedulingSimulation {

    public static void main(String[] args) {
        String filename;

        // Get filename from command line arguments
        if (args.length > 0) {
            filename = args[0];
            System.out.println("Using filename: " + filename);
        } else {
            // Use default filename if no argument provided
            filename = "datafile1.txt";
            System.out.println("No filename provided. Using default: " + filename);
        }

        try {
            // Read process data from file
            DataFileReader.ProcessData data = DataFileReader.readDataFile(filename);
            List<Process> processes = data.getProcesses();
            int dispatchTime = data.getDispatchTime();
            
            // Run FCFS scheduling
            SchedulingResult fcfsResult = SchedulingAlgorithms.fcfs(processes, dispatchTime);
            fcfsResult.printResults();
            
            // Run Round Robin scheduling
            SchedulingResult rrResult = SchedulingAlgorithms.roundRobin(processes, dispatchTime);
            rrResult.printResults();
            
            // Run Selfish Round Robin scheduling
            SchedulingResult srrResult = SelfishRoundRobin.selfishRoundRobin(processes, dispatchTime);
            srrResult.printResults();
            
            System.out.println();
            
        } catch (IOException e) {
            System.err.println("Error reading data file: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("An error occurred during simulation: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

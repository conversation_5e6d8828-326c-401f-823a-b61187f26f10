FILENAME = "datafile1.txt"


def process_data(filename):
    """
    Opens the data file, assembles the data, and returns it for use in the simulation
    """
    file = open(filename, "r")
    lines = file.readlines()

    # create a list of lists
    data = []
    for line in lines:
        data.append(line.split())



    for i in range(0, len(data)):
        if len(data[i]) > 0 and data[i][0] == "DISP:":
            # try : is the word after DISP: a non-negative integer
            try:
                if data[i][1].isdigit() and int(data[i][1])>=0:
                    dispatch = int(data[i][1])
                    break
            except:
                print("Error: Could not identify a valid dispatcher time")
                break

    # create a list of dictionaries containing the processes
    processes = []

    current_line = 0
    datalength = len(data)

    while current_line < datalength:
        if data[current_line] != [] and data[current_line][0] == "PID:":
            #add a new dictionary to the list of processes
            processes.append({})
            # add the name of the process to the dictionary
            processes[-1]["name"] = data[current_line][1]
            # add the integer value of the process id to the dictionary
            processes[-1]["id"] = int(data[current_line][1][1:])
            # add the integer value of the arrival time to the dictionary
            processes[-1]["arrival"] = int(data[current_line+1][1])
            # add the integer value of the server time to the dictionary
            processes[-1]["server_time"] = int(data[current_line+2][1])

        current_line += 1
    return processes, dispatch

def output(results):
    """
    Prints the results of the simulation in a similar format to the sample output
    """
    processes,history, waits, turnarounds = results
    print("")
    for h in history:
        print(h)
    print("")
    print("Process Turnarounds Waits ")
    for p in processes:
        print(f"{p['name']:<8}{turnarounds[p['name']]:<12}{waits[p['name']]}")

def FCFS(data):
    """Use the processes data dictionary including arrival time and server time to
       simulate FCFS scheduling. Use dispatch_cost to determine the cost of dispatching
       a process. Record all the times when the server begins to work on a process. For 
       each process, record the turnaround time and wait time"""
    processes = data[0]
    dispatch = data[1]
    time = 0
    ready_queue = []
    server_state = 'DISPATCHING'
    current_process = None
    waiting_time = {}
    turnaround_time = {}
    process_history= []
    process_history.append("FCFS:")
    finished = False
    # sort processes by arrival time and id
    processes = sorted(processes, key=lambda x: (x["arrival"], x["id"]))
    # for each process with an arrival time == 0, add it to the ready queue
    for process in processes:
        if process["arrival"] == 0:
            ready_queue.append(process)
            waiting_time[process["name"]] = 0
    while not finished:
        if server_state == 'DISPATCHING':
            if not ready_queue:
                finished = True
                break
            for process in ready_queue:
                waiting_time[process["name"]] += 1
            current_process = ready_queue.pop(0)
            process_history.append("T"+str(int(time+1))+": "+current_process["name"])
            server_state = 'WORKING'
        elif server_state == 'WORKING':
            # decrement the SrvTime of the current process
            current_process["server_time"] -= 1
            # if the current process is done, add it to the finished queue
            if current_process["server_time"] == 0:
                server_state = 'DISPATCHING'
                turnaround_time[current_process["name"]] = time+1 - current_process["arrival"]
            # for process on the ready queue, increment the wait time
            for process in ready_queue:
                waiting_time[process["name"]] += 1
        time += 1
        for process in processes: 
            if process["arrival"] == time:
                ready_queue.append(process)
                waiting_time[process["name"]] = 0
    return processes, process_history, waiting_time, turnaround_time

def RR(data):
    """Use the processes data dictionary including arrival time and server time to
       simulate Round Robin scheduling. Use dispatch_cost to determine the cost of dispatching
       a process. Record all the times when the server begins to work on a process. For 
       each process, record the turnaround time and wait time. Quant for each process is 3"""
    
    processes = data[0]
    dispatch = data[1]
    quant = 3
    current_quant = 0
    time = 0
    ready_queue = []
    server_state = 'DISPATCHING'
    current_process = None
    return_process = []
    waiting_time = {}
    turnaround_time = {}
    process_history= []
    process_history.append("RR:")
    finished = False
    # sort processes by arrival time and id
    processes = sorted(processes, key=lambda x: (x["arrival"], x["id"]))
    # for each process with an arrival time == 0, add it to the ready queue
    for process in processes:
        if process["arrival"] == 0:
            ready_queue.append(process)
            waiting_time[process["name"]] = 0
    while not finished:
        if server_state == 'DISPATCHING':
            if not ready_queue:
                finished = True
                break
            for process in ready_queue:
                waiting_time[process["name"]] += 1
            current_process = ready_queue.pop(0)
            process_history.append("T"+str(int(time+1))+": "+current_process["name"])
            server_state = 'WORKING'
            current_quant = 0
        elif server_state == 'WORKING':
            # decrement the SrvTime of the current process
            current_process["server_time"] -= 1
            current_quant += 1
            # if the current process is done, add it to the finished queue
            if current_process["server_time"] == 0:
                server_state = 'DISPATCHING'
                turnaround_time[current_process["name"]] = time+1 - current_process["arrival"]
            elif current_quant >= quant:
                server_state = 'DISPATCHING'
                return_process.append(current_process)
                current_process = None
            # for process on the ready queue, increment the wait time
            for process in ready_queue:
                waiting_time[process["name"]] += 1
        time += 1
        for process in processes: 
            if process["arrival"] == time:
                ready_queue.append(process)
                waiting_time[process["name"]] = 0
        if return_process:
            ready_queue.append(return_process[0])
            return_process = []

    return processes, process_history, waiting_time, turnaround_time

def SRR(data):
    """Use the processes data dictionary including arrival time and server time to
       simulate Round Robin scheduling. Use dispatch_cost to determine the cost of dispatching
       a process. Record all the times when the server begins to work on a process. For 
       each process, record the turnaround time and wait time. Quant for each process is 3"""
    
    processes = data[0]
    dispatch = data[1]
    quant = {}
    current_quant = 0
    time = 0
    ready_queue = []
    server_state = 'DISPATCHING'
    current_process = None
    return_process = []
    waiting_time = {}
    turnaround_time = {}
    process_history= []
    process_history.append("SRR:")
    finished = False
    # sort processes by arrival time and id
    processes = sorted(processes, key=lambda x: (x["arrival"], x["id"]))
    # for each process with an arrival time == 0, add it to the ready queue
    for process in processes:
        if process["arrival"] == 0:
            ready_queue.append(process)
            waiting_time[process["name"]] = 0
            quant[process["name"]] = 3
    while not finished:
        if server_state == 'DISPATCHING':
            if not ready_queue:
                finished = True
                break
            for process in ready_queue:
                waiting_time[process["name"]] += 1
            current_process = ready_queue.pop(0)
            process_history.append("T"+str(int(time+1))+": "+current_process["name"])
            server_state = 'WORKING'
            current_quant = 0
        elif server_state == 'WORKING':
            # decrement the SrvTime of the current process
            current_process["server_time"] -= 1
            current_quant += 1
            # if the current process is done, add it to the finished queue
            if current_process["server_time"] == 0:
                server_state = 'DISPATCHING'
                turnaround_time[current_process["name"]] = time+1 - current_process["arrival"]
            elif current_quant >= quant[current_process["name"]]:
                server_state = 'DISPATCHING'
                quant[current_process["name"]] += 1
                quant[current_process["name"]] = min(quant[current_process["name"]], 6)
                return_process.append(current_process)
                current_process = None
            # for process on the ready queue, increment the wait time
            for process in ready_queue:
                waiting_time[process["name"]] += 1
        time += 1
        for process in processes: 
            if process["arrival"] == time:
                ready_queue.append(process)
                waiting_time[process["name"]] = 0
                quant[process["name"]] = 3
        if return_process:
            ready_queue.append(return_process[0])
            return_process = []

    return processes, process_history, waiting_time, turnaround_time
results = FCFS(process_data(FILENAME))
output(results)
results = RR(process_data(FILENAME))
output(results)
results = SRR(process_data(FILENAME))
output(results)
print("")


